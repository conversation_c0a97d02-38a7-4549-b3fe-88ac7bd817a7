'use client'
import { useEffect, useRef } from 'react'
import Image from 'next/image'
import { gsap, Draggable } from '@/lib/gsap'

function NewBadge(){ return <span className="badge-new">NEW</span> }

type MiniEditorialCardProps = {
  title: string
  image: string
  alt?: string
  children: React.ReactNode
}

function MiniEditorialCard({ title, image, alt, children }: MiniEditorialCardProps) {
  return (
    <article className="px-4 md:px-6 py-8">
      <div className="parallax-wrap relative aspect-[16/9] rounded-lg overflow-hidden mb-4">
        <Image src={image} alt={alt ?? title} fill className="parallax-img object-cover will-change-transform" />
        <div className="pointer-events-none absolute inset-0 bg-[radial-gradient(transparent_60%,rgba(0,0,0,.14))] opacity-35" />
      </div>
      <h4 className="font-display text-[22px] uppercase leading-none tracking-[.02em] flex items-center gap-2">
        {title} <NewBadge />
      </h4>
      <p className="mt-2 text-[15px] leading-6 text-ink/80">{children}</p>
    </article>
  )
}

function CenterCopy(){
  return (
    <div className="px-4 md:px-6 py-8">
      <div className="font-display text-5xl md:text-[56px] leading-[0.95]">ALL WORK!</div>
      <div className="font-display text-[26px] md:text-[30px] leading-[1.05] mt-3">
        A Featured selection<br/> the latest work —<br/> of the last years.
      </div>
      <div className="mt-4 text-[12px] uppercase tracking-wider text-ink/70">
        <strong>Tip!</strong> Drag sideways to navigate
      </div>
    </div>
  )
}

export default function HeaderStrip(){
  const sectionRef = useRef<HTMLElement>(null)
  const wrapRef = useRef<HTMLDivElement>(null)   // viewport
  const trackRef = useRef<HTMLDivElement>(null)  // draggable row

  // reveal + image parallax
  useEffect(() => {
    const section = sectionRef.current
    if (!section) return
    gsap.from(section.querySelectorAll('[data-reveal] > *'), {
      opacity: 0, y: 14, stagger: 0.06, duration: 0.5, ease: 'power2.out'
    })

    // parallax hover (desktop)
    const cards = Array.from(section.querySelectorAll<HTMLDivElement>('.parallax-wrap'))
    const enter = (img: HTMLElement) => gsap.to(img, { scale: 1.04, duration: 0.28, ease: 'power3.out' })
    const leave = (img: HTMLElement) => gsap.to(img, { x: 0, y: 0, rotate: 0, scale: 1, duration: 0.4, ease: 'power3.out' })
    const move  = (img: HTMLElement, e: MouseEvent) => {
      const r = (img.parentElement as HTMLElement).getBoundingClientRect()
      const mx = (e.clientX - r.left)/r.width - 0.5
      const my = (e.clientY - r.top)/r.height - 0.5
      gsap.to(img, { x: mx*14, y: my*14, rotate: mx*1.2, overwrite: true, duration: 0.22, ease: 'power2.out' })
    }
    const off: Array<() => void> = []
    cards.forEach(card => {
      const img = card.querySelector('.parallax-img') as HTMLElement
      const onEnter = () => enter(img)
      const onLeave = () => leave(img)
      const onMove  = (e: MouseEvent) => move(img, e)
      card.addEventListener('mouseenter', onEnter)
      card.addEventListener('mouseleave', onLeave)
      card.addEventListener('mousemove', onMove)
      off.push(() => { card.removeEventListener('mouseenter', onEnter); card.removeEventListener('mouseleave', onLeave); card.removeEventListener('mousemove', onMove) })
    })
    return () => off.forEach(fn => fn())
  }, [])

  // GSAP Draggable row with snap + wheel->horizontal
  useEffect(() => {
    const wrap  = wrapRef.current!
    const track = trackRef.current!
    let snapTimer: any = null

    const panels = Array.from(track.querySelectorAll<HTMLElement>('.panel'))
    const getMinX = () => Math.min(0, wrap.clientWidth - track.scrollWidth)

    // set initial
    gsap.set(track, { x: 0 })

    // create draggable
    const draggable = Draggable.create(track, {
      type: 'x',
      bounds: { minX: getMinX(), maxX: 0 },
      inertia: false,          // keep free-version friendly
      dragClickables: true,
      onPress(){ wrap.classList.add('dragging') },
      onRelease(){ wrap.classList.remove('dragging'); snapToClosest() },
    })[0]

    // snap to closest panel
    function snapToClosest() {
      const x = draggable.x
      let best = 0, min = Infinity
      for (const p of panels) {
        const goal = -p.offsetLeft // move so this panel aligns to left
        const d = Math.abs(x - goal)
        if (d < min) { min = d; best = goal }
      }
      const target = gsap.utils.clamp(getMinX(), 0, best)
      gsap.to(track, { x: target, duration: 0.35, ease: 'power3.out', onUpdate: () => draggable.update() })
    }

    // on resize, recompute bounds & (optionally) re-snap
    const ro = new ResizeObserver(() => {
      draggable.applyBounds({ minX: getMinX(), maxX: 0 })
      snapToClosest()
    })
    ro.observe(wrap); ro.observe(track)

    // wheel -> horizontal (good for trackpads)
    const onWheel = (e: WheelEvent) => {
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        const minX = getMinX()
        const next = gsap.utils.clamp(minX, 0, draggable.x - e.deltaY)
        gsap.to(track, { x: next, duration: 0.15, ease: 'power2.out', onUpdate: () => draggable.update() })
        e.preventDefault()
        if (snapTimer) clearTimeout(snapTimer)
        snapTimer = setTimeout(snapToClosest, 160)
      }
    }
    wrap.addEventListener('wheel', onWheel, { passive: false })

    return () => {
      ro.disconnect()
      wrap.removeEventListener('wheel', onWheel as any)
      draggable.kill()
    }
  }, [])

  return (
    <section ref={sectionRef} className="no-wash relative z-[3] border-y border-ink/15">
      {/* viewport */}
      <div ref={wrapRef} className="headerstrip-viewport mx-auto max-w-6xl overflow-hidden">
        {/* track */}
        <div ref={trackRef} className="headerstrip-track flex select-none">
          {/* PANEL 1: left column (you can stack cards vertically) */}
          <div className="panel shrink-0 border-l first:border-l-0 border-ink/20" data-reveal>
            <MiniEditorialCard title="AvroKO" image="/hero/thumbnail-small.jpeg" alt="AvroKO">
              AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture
              for hospitality, restaurant and bars.
            </MiniEditorialCard>

            {/* extra sample item */}
            <MiniEditorialCard title="AvroKO" image="/hero/thumbnail-small.jpeg" alt="AvroKO">
              AvroKO is an award-winning global design firm, established itself as a global leader in interior architecture
              for hospitality, restaurant and bars.
            </MiniEditorialCard>
          </div>

          {/* PANEL 2: center copy */}
          <div className="panel shrink-0 border-l border-ink/20" data-reveal>
            <CenterCopy />
          </div>

          {/* PANEL 3: right column */}
          <div className="panel shrink-0 border-l border-ink/20" data-reveal>
            <MiniEditorialCard title="The Roger Hub" image="/hero/thumbnail-small.jpeg" alt="The Roger Hub">
              The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration
              born out of a partnership with the legendary Roger Federer.
            </MiniEditorialCard>

            {/* extra sample item */}
            <MiniEditorialCard title="The Roger Hub" image="/hero/thumbnail-small.jpeg" alt="The Roger Hub">
              The Roger Hub is an immersive web experience showcasing the tennis-inspired ‘On’ sneakers, a collaboration
              born out of a partnership with the legendary Roger Federer.
            </MiniEditorialCard>
          </div>

          {/* add more .panel blocks if you want more “columns” */}
        </div>
      </div>
    </section>
  )
}
